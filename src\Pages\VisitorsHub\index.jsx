import React, { useState, useRef, useEffect, useCallback } from "react";

import DateInput from "../../Components/Global/Input/ValidationHubDate";
import GenericTable from "../../Components/GenericTable";
import DetailsCard from "../../Components/Global/DetailsCard";
import PrintModal from "../../Components/Global/PrintModal";
import EditPhotoModal from "../../Components/Global/ImageAndCamera/EditPhotoModal";
import HostSearch from "./HostSearch";
import VisitorSearch from "./VisitorSearch";
import useClickOutside from "../InpatientVisit/useClickOutside";
import Button from "../../Components/Global/Button";
import VisitorForm from "../../Components/Global/Forms/VisitorForm";
import defaultImg from "../../Images/demoimg.svg"
import moment from "moment";
import { FilterButtons } from "../../Components/GenericTable";
// import { VisitorsDoctorsData } from "../../api/static"
import { getVisitorColumns } from "../../api/tableDataColumns"; 
import homeicon from "../../Images/home-icon.svg";
import CreateVisitorModal from "../../Components/Global/CreateVisitorModal"; // Import the modal for creating a new visitor
import { searchIdentities } from "../../api/global"; // <-- import the API
import formatDateTime from "../../utils/formatDate";
import { getVisitGuests } from "../../api/visitor-hub";
import { searchGuests } from "../../api/guest";
import Loader from "../../Components/Loader";

const ReceptionDesk = () => {
  // ---------------- State Variables ----------------
  const [searchTerm, setSearchTerm] = useState("");
  const [tableSearchTerm, setTableSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [guestSearchTerm, setGuestSearchTerm] = useState("");
  const [guestSearchResults, setGuestSearchResults] = useState([]);
  const [isGuestDropdownVisible, setIsGuestDropdownVisible] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [selectedHost, setSelectedHost] = useState(null); // Track selected host for DetailsCard

  // State for the complete list of visitors (across all hosts)
  const [allGuests, setAllGuests] = useState([]);
  const [selectedGuest, setSelectedGuest] = useState(null); // Track selected guest for DetailsCard
  const [visitId, setVisitId] = useState(""); // Replace with real value or from props/context

  // We'll derive our final list based on the selected date and filter button.
  const [dateFilteredGuests, setDateFilteredGuests] = useState([]);
  const [activeFilter, setActiveFilter] = useState("all");
  const [printModalVisible, setPrintModalVisible] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedGuestId, setSelectedGuestId] = useState(null);
  const [showVisitorForm, setShowVisitorForm] = useState(false);
  const [patientSearchPlaceholder, setPatientSearchPlaceholder] = useState("Search By Host Name, EID");
  const [guestSearchPlaceholder, setGuestSearchPlaceholder] = useState("Search By Guest Name");

  // New state for custom date picker
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [isCreateVisitorModalOpen, setIsCreateVisitorModalOpen] = useState(false); // State for the create visitor modal

  // Sorting state
  const [sortBy, setSortBy] = useState("visit_created_at");
  const [sortOrder, setSortOrder] = useState("DESC");

  // Loading state
  const [loading, setLoading] = useState(false);

  // Fetch guests from API with proper parameters
  const fetchGuests = useCallback(async (params = {}) => {
    setLoading(true);
    try {
      const fetFacilityId = localStorage.getItem("selectedFacility");

      if (!fetFacilityId) {
        setAllGuests([]);
        setDateFilteredGuests([]);
        return;
      }

      // Build API parameters
      const apiParams = {
        search: tableSearchTerm || undefined,
        sortBy: sortBy || "visit_created_at",
        sortOrder: sortOrder || "DESC",
        ...params
      };

      // Add filter-specific parameters based on activeFilter
      // Only add filter parameters when a specific filter is selected (not "all")
      if (activeFilter === "recent") {
        apiParams.recent_visitors_time = 120; // Recent visitors within 2 hours
      } else if (activeFilter === "invited") {
        apiParams.is_invited = true;
      } else if (activeFilter === "checkedin") {
        apiParams.is_checked_in = true;
      } else if (activeFilter === "checkedout") {
        apiParams.is_checkedout = true;
        console.log("Setting is_checkedout to true", apiParams);
      } else if (activeFilter === "checkInDenied") {
        apiParams.is_checkin_denied = true;
      }
      // For "all" filter, we don't add any boolean filter parameters

      // Add date filter if selectedDate is not today
      const today = moment().format("YYYY-MM-DD");
      const selectedDateFormatted = moment(selectedDate).format("YYYY-MM-DD");
      if (selectedDateFormatted !== today) {
        apiParams.checkin_date = selectedDateFormatted;
      }

      // Add host filter if a patient is selected
      if (selectedPatient?.id) {
        apiParams.host_id = selectedPatient.id;
      }

      console.log("=== API CALL START ===");
      console.log("Active Filter:", activeFilter);
      console.log("API Parameters being sent:", apiParams);
      console.log("Timestamp:", new Date().toISOString());

      const response = await getVisitGuests(fetFacilityId, visitId, apiParams);
      const guests = response?.data?.data || [];

      console.log("=== API CALL END ===");
      console.log("Received guests count:", guests.length);

      setAllGuests(guests);
      setDateFilteredGuests(guests);
    } catch (error) {
      console.error("Error fetching guests:", error);
      setAllGuests([]);
      setDateFilteredGuests([]);
    } finally {
      setLoading(false);
    }
  }, [tableSearchTerm, sortBy, sortOrder, activeFilter, selectedDate, selectedPatient, visitId]);

  // Single useEffect to handle all data fetching with proper debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchGuests();
    }, tableSearchTerm ? 500 : 0); // 500ms debounce for search, immediate for other changes

    return () => clearTimeout(timeoutId);
  }, [tableSearchTerm, sortBy, sortOrder, activeFilter, selectedDate, selectedPatient, visitId, fetchGuests]);

  // All state variables are declared above - no duplicates needed here

  // Refs for clickOutside logic
  const patientSearchRef = useRef(null);
  const guestSearchRef = useRef(null);
  useClickOutside(guestSearchRef, () => setIsGuestDropdownVisible(false));
  useClickOutside(patientSearchRef, () => setIsDropdownVisible(false));

  // ---------------- Handlers ----------------
  const handleHome = () => {
    setSelectedPatient(null);
    setSelectedHost(null);
    setSelectedGuest(null);
    setPatientSearchPlaceholder("Search By Host Name, EID");
    setGuestSearchPlaceholder("Search By Guest Name");
    setActiveFilter("all");
    setTableSearchTerm("");

    // Re-fetch all visitors from API
    fetchGuests();
  };

  const handleHostSearchInputChange = async (value) => {
    setSearchTerm(value);
    if (value.trim()) {
      // Use the API to search for hosts
      try {
        const apiResults = await searchIdentities(value);
        console.log("API response from /master-data/identity-hub:", apiResults);
        const results = apiResults?.data?.data || [];
        console.log("Processed search results:", results);
        setSearchResults(results);
        setIsDropdownVisible(true);
      } catch (error) {
        console.error("Error searching identities:", error);
        setSearchResults([]);
        setIsDropdownVisible(false);
      }
    } else {
      setSearchResults([]);
      setIsDropdownVisible(false);
    }
  };

  const handleHostClick = (host) => {
    console.log("Selected host from /master-data/identity-hub:", host);
    setSelectedPatient(host);
    setSelectedHost(host); // Set selected host for DetailsCard
    setSelectedGuest(null); // Clear guest selection
    // Set visitId from host, which triggers useEffect to fetch guests
    setVisitId(host.visitId || host.id || null);
    setIsDropdownVisible(false);
    setSearchTerm("");
    setPatientSearchPlaceholder(host.name || `${host.first_name || ''} ${host.last_name || ''}`.trim());
    setGuestSearchPlaceholder("Search By Guest Name"); // Reset guest placeholder
    setShowVisitorForm(false);

    // Filter the table to show only guests for this host
    fetchGuests({ host_id: host.id });
  };

  const handleVisitorSearchInputChange = async (value) => {
    setGuestSearchTerm(value);
    if (value.trim()) {
      // Try all possible fields for search
      const params = {
        first_name: value,
        last_name: value,
        email: value,
        phone: value,
      };
      try {
        const apiResults = await searchGuests(params);
        setGuestSearchResults(apiResults.data.data || []);
        setIsGuestDropdownVisible(true);
      } catch (error) {
        setGuestSearchResults([]);
        setIsGuestDropdownVisible(false);
      }
    } else {
      setGuestSearchResults([]);
      setIsGuestDropdownVisible(false);
    }
  };

  const handleGuestClick = (visitor) => {
    console.log("Selected guest from search:", visitor);
    // Set selected guest for DetailsCard
    setSelectedGuest(visitor);
    setSelectedHost(null); // Clear host selection
    setIsGuestDropdownVisible(false);
    setGuestSearchResults([]);
    setGuestSearchTerm("");
    setGuestSearchPlaceholder(visitor.visitorName || visitor.guest_name || visitor.name || "Selected Guest");
    setPatientSearchPlaceholder("Search By Host Name, EID"); // Reset host placeholder
    setShowVisitorForm(false);

    // Filter the table to show only the selected guest
    // First, find the guest in the current data or fetch it
    const guestInCurrentData = allGuests.find(guest =>
      guest.id === visitor.id ||
      guest.guest_id === visitor.id ||
      guest.guest_name === visitor.visitorName ||
      guest.guest_name === visitor.guest_name ||
      guest.guest_name === visitor.name
    );

    if (guestInCurrentData) {
      // If guest is found in current data, show only that guest
      setDateFilteredGuests([guestInCurrentData]);
    } else {
      // If guest is not in current data, fetch guests with guest_id filter
      fetchGuests({ guest_id: visitor.id });
    }
  };

  const handleCreateVisitorClick = () => {
    setIsCreateVisitorModalOpen(true); // Open the create visitor modal
  };

  const handleCreateVisitorSubmit = (newVisitor) => {
    // Add the new visitor to the list
    if (selectedPatient) {
      newVisitor.hostName = selectedPatient.name;
      newVisitor.facility = selectedPatient.site;
    }
    const updatedAllGuests = [newVisitor, ...allGuests];
    setAllGuests(updatedAllGuests);
    setDateFilteredGuests(updatedAllGuests);
    setIsCreateVisitorModalOpen(false); // Close the modal
  };

  const handleImageCaptured = (imageData) => {
    const updatedGuests = dateFilteredGuests.map((guest) =>
      guest.id === selectedGuestId ? { ...guest, image: imageData } : guest
    );
    setAllGuests(updatedGuests);
    setDateFilteredGuests(updatedGuests);
    setIsModalOpen(false);
  };

  const openModal = (title, guestId) => {
    setSelectedGuestId(guestId);
    setIsModalOpen(true);
  };

  const handlePrintClick = (guest) => {
    setSelectedGuest(guest);
    setPrintModalVisible(true);
  };

  const handleClosePrintModal = () => {
    setPrintModalVisible(false);
    setSelectedGuest(null);
  };

  const handleAddVisitor = (newVisitor) => {
    if (selectedPatient) {
      newVisitor.hostName = selectedPatient.name;
      newVisitor.facility = selectedPatient.site;
    }
    const updatedAllGuests = [newVisitor, ...allGuests];
    setAllGuests(updatedAllGuests);
    setDateFilteredGuests(updatedAllGuests);
    console.log("Visitor Added:", newVisitor);
  };

  const handleHistoryOpen = () => {
    console.log("History panel opened");
  };

  // Handle filter change
  const handleFilterChange = (newFilter) => {
    // Only update state if filter is actually different
    if (activeFilter !== newFilter) {
      console.log('Changing filter from', activeFilter, 'to', newFilter);
      setActiveFilter(newFilter);
      // The useEffect will automatically trigger a new API call with the updated filter
    } else {
      console.log('Filter unchanged, skipping update');
    }
  };

  // Handle sorting functionality
  const handleSort = (column, sortDirection) => {
    console.log('ReceptionDesk - Sort clicked:', { column, sortDirection });

    // Extract column identifier from column object
    const columnId = column.id || column.selector || column.name;
    console.log('Sorting by column:', columnId, 'Direction:', sortDirection);

    // Map frontend column IDs to API column names
    let apiSortBy = columnId;
    switch (columnId) {
      case 'guest_name':
        apiSortBy = 'guest_name';
        break;
      case 'host_name':
        apiSortBy = 'host_name';
        break;
      case 'facility_name':
        apiSortBy = 'facility_name';
        break;
      case 'check_in_time':
        apiSortBy = 'check_in_time';
        break;
      case 'check_out_time':
        apiSortBy = 'check_out_time';
        break;
      default:
        apiSortBy = 'visit_created_at';
    }

    // Only update state if values are actually different to prevent unnecessary re-renders
    if (sortBy !== apiSortBy || sortOrder !== sortDirection.toUpperCase()) {
      console.log('Updating sort state:', { apiSortBy, sortDirection: sortDirection.toUpperCase() });
      setSortBy(apiSortBy);
      setSortOrder(sortDirection.toUpperCase());
      // The useEffect will automatically trigger a new API call with the updated sort parameters
    } else {
      console.log('Sort state unchanged, skipping update');
    }
  };
  // console.log(selectedPatient);


  // ---------------- Date Filtering ----------------
  const filterVisitorsByDate = (date) => {
    const formattedSelectedDate = moment(date).format("M-D-YYYY");
    const filteredVisitors = allGuests.filter((visitor) => {
      const visitorDate = moment(visitor.startDate, "M-D-YYYY h:mm A").format("M-D-YYYY");
      return visitorDate === formattedSelectedDate;
    });
    setDateFilteredGuests(filteredVisitors);
    setShowVisitorForm(false)
  };
  // Refresh function to reload guests data
  const refreshGuestsData = () => {
    const fetFacilityId = localStorage.getItem("selectedFacility");
    if (fetFacilityId) {
      getVisitGuests(fetFacilityId)
        .then((response) => {
          const guests = response?.data?.data || [];
          setAllGuests(guests);
          setDateFilteredGuests(guests);
        })
        .catch(() => {
          setAllGuests([]);
          setDateFilteredGuests([]);
        });
    }
  };

  const visitorColumns = getVisitorColumns({
    openModal,
    handlePrintClick,
    profileImage: defaultImg,
    onRefresh: refreshGuestsData,
  });
  // ---------------- Derived Data ----------------
  // Apply the active filter on top of the date filtered guests.
  const displayedGuestList = Array.isArray(dateFilteredGuests) ? dateFilteredGuests : [];

  // Filter options for the FilterButtons component
  const filterOptions = [
    { value: "recent", label: "Recent visitors" },
    { value: "all", label: "All Visitors" },
    { value: "invited", label: "All Invited" },
    { value: "checkedin", label: "Checked In" },
    { value: "checkedout", label: "Checked Out" },
    { value: "checkInDenied", label: "Check-in-denied" }
  ];

  return (
    <div className="pl-24 mt-20 pr-8 h-full">
      {/* Title */}
      <div className="text-[24px] font-normal text-[#4F2683]">
        <h3>Reception Desk</h3>
      </div>

      {/* Top Search, Custom Date Picker, Filter Buttons & Home Icon Row */}
      <div>
        <div className="flex flex-col sm:flex-row sm:justify-center items-center  sm:gap-6 my-4 mb-8">

          <Button type="imgbtn" className="px-[10px] py-2" icon={homeicon} onClick={handleHome} />

          {/* Host Search */}
          <HostSearch
            placeholder={patientSearchPlaceholder}
            searchTerm={searchTerm}
            onInputChange={(value) => {
              setSearchTerm(value);
              handleHostSearchInputChange(value); // your existing logic
            }}
            results={searchResults}
            onResultClick={handleHostClick}
            isDropdownVisible={isDropdownVisible}
            containerRef={patientSearchRef}
          />

          {/* Visitor Search */}
          <VisitorSearch
            placeholder={guestSearchPlaceholder}
            searchTerm={guestSearchTerm}
            onInputChange={handleVisitorSearchInputChange}
            results={guestSearchResults}
            onResultClick={handleGuestClick}
            isDropdownVisible={isGuestDropdownVisible}
            containerRef={guestSearchRef}
            onCreateClick={handleCreateVisitorClick} // Pass the create visitor handler
          />

          {/* Custom Date Picker */}
          <DateInput
            value={selectedDate}
            onChange={(date) => {
              setSelectedDate(date);
              filterVisitorsByDate(date);
            }}
            placeholder="Select a date"
            className="w-[25%]  rounded-md text-[#4F2683] "
          />

        </div>
      </div>
      {/* Render DetailsCard if a host is selected */}
      {selectedHost && (
        <DetailsCard
          OpenPhotoModal={() => setIsModalOpen(true)}
          handleHistoryOpen={handleHistoryOpen}
          profileImage={selectedHost?.image || defaultImg}
          defaultImage={defaultImg}
          name={selectedHost?.name || `${selectedHost?.first_name || ''} ${selectedHost?.last_name || ''}`.trim() || "N/A"}
          showHistoryButton={false}
          additionalFields={[
            {
              label: "EID",
              value: selectedHost?.eid || "N/A"
            },
            {
              label: "Job Title",
              value: selectedHost?.jobTittle || selectedHost?.post || "N/A"
            },
            {
              label: "Department",
              value: selectedHost?.department || "N/A"
            },
            {
              label: "Site",
              value: selectedHost?.site || "N/A"
            }
          ]}
        />
      )}
      {/* Render DetailsCard if a guest is selected */}
      {selectedGuest && (
        <DetailsCard
          OpenPhotoModal={() => setIsModalOpen(true)}
          handleHistoryOpen={handleHistoryOpen}
          profileImage={selectedGuest?.image || defaultImg}
          defaultImage={defaultImg}
          name={selectedGuest?.visitorName || selectedGuest?.name || "N/A"}
          showHistoryButton={false}
          additionalFields={[
            {
              label: "Email",
              value: selectedGuest?.email || "N/A"
            },
            {
              label: "Phone",
              value: selectedGuest?.phone || "N/A"
            },
            {
              label: "Status",
              value: selectedGuest?.status || "N/A"
            },
            {
              label: "Start Date",
              value: selectedGuest?.startDate || "N/A",
            },
            {
              label: "End Date",
              value: selectedGuest?.endDate ? formatDateTime(selectedGuest.endDate) : "N/A",
            }
          ]}
        />
      )}
      {/* Render Visitor Form */}
      {showVisitorForm && (
        <VisitorForm
          onAddGuest={handleAddVisitor}
          onClose={() => setShowVisitorForm(false)}
          hostName={selectedPatient?.name || `${selectedPatient?.first_name || ''} ${selectedPatient?.last_name || ''}`.trim()} // Pass hostName to VisitorForm
          hostId={selectedPatient?.id} // Pass hostId from /master-data/identity-hub API (primary key)
          selectedHost={selectedPatient} // Pass complete host object
          escortId={null} // Can be added later when escort selection is implemented
          selectedEscort={null} // Can be added later when escort selection is implemented
        />
      )}
      {/* Show table and Add button if host is selected */}
      {selectedHost && (
        <>
          {/* Show selected host indicator */}
          {/* <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-green-700 font-medium">Selected Host:</span>
                <span className="text-green-900 font-semibold">
                  {selectedHost.name || `${selectedHost.first_name || ''} ${selectedHost.last_name || ''}`.trim() || "Unknown Host"}
                </span>
              </div>
              <button
                onClick={() => {
                  setSelectedHost(null);
                  setPatientSearchPlaceholder("Search By Host Name, EID");
                  fetchGuests(); // Reload all guests
                }}
                className="text-green-600 hover:text-green-800 text-sm underline"
              >
                Clear Selection
              </button>
            </div>
          </div> */}

          <FilterButtons filter={activeFilter} onFilterChange={handleFilterChange} filterOptions={filterOptions} />
          <div className="mt-4">
            {loading ? (
              <Loader />
            ) : (
              <GenericTable
                title={"Guests"}
                searchTerm={tableSearchTerm}
                onSearchChange={setTableSearchTerm}
                onSort={handleSort}
                columns={visitorColumns}
                data={displayedGuestList}
                fixedHeader
                onAdd={() => setShowVisitorForm(true)}
                fixedHeaderScrollHeight="300px"
                highlightOnHover
                showAddButton={true}
                striped={false}
                passValueToSearch={true}
              />
            )}
          </div>
        </>
      )}
      {/* Show table and Add button if guest is selected */}
      {selectedGuest && (
        <>
          {/* Show selected guest indicator */}
          {/* <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-blue-700 font-medium">Selected Guest:</span>
                <span className="text-blue-900 font-semibold">
                  {selectedGuest.visitorName || selectedGuest.guest_name || selectedGuest.name || "Unknown Guest"}
                </span>
              </div>
              <button
                onClick={() => {
                  setSelectedGuest(null);
                  setGuestSearchPlaceholder("Search By Guest Name");
                  fetchGuests(); // Reload all guests
                }}
                className="text-blue-600 hover:text-blue-800 text-sm underline"
              >
                Clear Selection
              </button>
            </div>
          </div> */}

          <FilterButtons filter={activeFilter} onFilterChange={handleFilterChange} filterOptions={filterOptions} />
          <div className="mt-4">
            {loading ? (
              <Loader />
            ) : (
              <GenericTable
                title={"Guests"}
                searchTerm={tableSearchTerm}
                onSearchChange={setTableSearchTerm}
                onSort={handleSort}
                columns={visitorColumns}
                data={displayedGuestList}
                fixedHeader
                onAdd={() => setShowVisitorForm(true)}
                fixedHeaderScrollHeight="300px"
                highlightOnHover
                showAddButton={true}
                striped={false}
                passValueToSearch={true}
              />
            )}
          </div>
        </>
      )}
      {/* If no host or guest is selected, show table as before */}
      {!selectedHost && !selectedGuest && (
        <>
          <FilterButtons filter={activeFilter} onFilterChange={handleFilterChange} filterOptions={filterOptions} />
          <div className="mt-4">
            {loading ? (
              <Loader />
            ) : (
              <GenericTable
                title={"Guests"}
                searchTerm={tableSearchTerm}
                onSearchChange={setTableSearchTerm}
                onSort={handleSort}
                columns={visitorColumns}
                data={displayedGuestList}
                fixedHeader
                onAdd={() => setShowVisitorForm(true)}
                fixedHeaderScrollHeight="300px"
                highlightOnHover
                showAddButton={selectedPatient ? true : false}
                striped={false}
                passValueToSearch={true}
              />
            )}
          </div>
        </>
      )}

      {/* Print Modal */}
      {printModalVisible && selectedGuest && (
        <PrintModal guest={selectedGuest} onClose={handleClosePrintModal} />
      )}

      {/* Edit Photo Modal */}
      {isModalOpen && (
        <EditPhotoModal onClose={() => setIsModalOpen(false)} onSave={handleImageCaptured} />
      )}

      {/* Create Visitor Modal */}
      {isCreateVisitorModalOpen && (
        <CreateVisitorModal
          isOpen={isCreateVisitorModalOpen}
          onClose={() => setIsCreateVisitorModalOpen(false)}
          onSubmit={handleCreateVisitorSubmit}
          onHostSelect={handleHostClick} // Pass the same handler as HostSearch
        />
      )}
    </div>
  );
};

export default ReceptionDesk;
