import api from "./";

/**
 * Create a new guest.
 *
 * @param {object} guestData - The data for the new guest.
 * @returns {Promise<any>} A promise that resolves to the created guest data.
 */
export const createGuest = async (guestData) => {
  const response = await api.post("/guests", guestData);
  return response.data;
};

/**
 * Get all guests with pagination and filtering.
 *
 * @param {object} params - Query parameters for filtering and pagination.
 * @returns {Promise<any>} A promise that resolves to the list of guests.
 */
export const getGuests = async (params = {}) => {
  const query = {};
  if (params.search) query.search = params.search;
  if (params.sortBy) query.sortBy = params.sortBy;
  if (params.sortOrder) query.sortOrder = params.sortOrder;
  if (params.page) query.page = params.page;
  if (params.limit) query.limit = params.limit;

  const response = await api.get("/guests", { params: query });
  return response.data;
};

/**
 * Search guests by email, phone, first name, or last name.
 *
 * @param {object} params - Query parameters for searching guests.
 * @returns {Promise<any>} A promise that resolves to the search results.
 */
export const searchGuests = async (params = {}) => {
  const query = {};
  if (params.email) query.search = params.email;
  if (params.phone) query.search = params.phone;
  if (params.first_name) query.search = params.first_name;
  if (params.last_name) query.search = params.last_name;

  const response = await api.get("/guests/search", { params: query });
  return response.data;
};

/**
 * Get a guest by ID.
 *
 * @param {string} guestId - The ID of the guest to retrieve.
 * @returns {Promise<any>} A promise that resolves to the guest data.
 */
export const getGuestById = async (guestId) => {
  const response = await api.get(`/guests/${guestId}`);
  return response.data;
};

/**
 * Update a guest by ID.
 *
 * @param {string} guestId - The ID of the guest to update.
 * @param {object} guestData - The data to update the guest.
 * @returns {Promise<any>} A promise that resolves to the updated guest data.
 */
export const updateGuest = async (guestId, guestData) => {
  const response = await api.put(`/guests/${guestId}`, guestData);
  return response.data;
};

/**
 * Delete a guest by ID.
 *
 * @param {string} guestId - The ID of the guest to delete.
 * @returns {Promise<any>} A promise that resolves to the deletion result.
 */
export const deleteGuest = async (guestId) => {
  const response = await api.delete(`/guests/${guestId}`);
  return response.data;
};

